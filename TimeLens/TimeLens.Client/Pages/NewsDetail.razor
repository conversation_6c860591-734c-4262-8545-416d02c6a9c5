@page "/news/{Id}"
@rendermode InteractiveAuto
@using TimeLens.Client.Shared
@inject NewsService NewsService
@inject NavigationManager NavigationManager


<div class="news-detail">
    <div class="content-container">
        <div class="news-header">
            <AntDesign.Button Icon="arrow-left" Type="ButtonType.Text"
                              OnClick="@(() => NavigationManager.NavigateTo("/"))">
                返回列表
            </AntDesign.Button>
        </div>

        @if (_isLoading)
        {
            <div class="loading">
                <Spin Size="SpinSize.Large" />
                <p>加载中...</p>
            </div>
        }
        else if (_newsItem == null)
        {
            <div class="error">
                <Result Status="ResultStatus.Http404"
                        Title="404"
                        SubTitle="抱歉，您访问的新闻不存在。">
                    <Template>
                        <AntDesign.Button Type="ButtonType.Primary" OnClick="@(() => NavigationManager.NavigateTo("/"))">
                            返回首页
                        </AntDesign.Button>
                    </Template>
                </Result>
            </div>
        }
        else
        {
            <div class="news-header">
                <h1>@_newsItem.Title</h1>
                <div class="news-meta">
                    <span>@_newsItem.Company</span>
                    <span>•</span>
                    <span>@_newsItem.PublishDate.ToString("yyyy-MM-dd HH:mm")</span>
                </div>
                <div class="news-tags">
                    <AntDesign.Tag Color="TagColor.Blue">@_newsItem.Category</AntDesign.Tag>
                    @* @foreach (var tag in _newsItem.Tags) *@
                    @* { *@
                    @*     <Tag Color="TagColor.Green">@tag</Tag> *@
                    @* } *@
                </div>
            </div>

            <div class="news-content">
                @((MarkupString)_newsItem.Content)
            </div>

            @if (_newsItem.ConceptThemes.Count > 0)
            {
                <div class="company-info">
                    <h2>关联概念题材</h2>

                    <Table DataSource="@_newsItem.ConceptThemes" Bordered="true" Size="TableSize.Small" HidePagination="true">
                        <Column TData="string" Title="概念题材" DataIndex="Name"/>
                        <Column TData="int" Title="热度" DataIndex="Heat">
                            <CellRender Context="cell">
                                <div class="heat-indicator">
                                    <span class="heat-value">@cell.FieldValue</span>
                                    <div class="heat-bar">
                                        <div class="heat-fill" style="width: @(cell.FieldValue)%"></div>
                                    </div>
                                </div>
                            </CellRender>
                        </Column>
                        <Column TData="List<string>" Title="活跃个股" DataIndex="ActiveStocks">
                            <CellRender Context="cell">
                                <div class="active-stocks">
                                    @foreach (var stock in cell.FieldValue)
                                    {
                                        <Tag Color="TagColor.Blue" Style="margin: 2px;">@stock</Tag>
                                    }
                                </div>
                            </CellRender>
                        </Column>
                    </Table>
                </div>
            }

            @if (_relatedNews.Any())
            {
                <div class="related-news">
                    <h2>相关报道</h2>
                    @foreach (var item in _relatedNews)
                    {
                        <NewsListItem NewsItem="item" OnItemClick="OnRelatedNewsClick"/>
                    }
                </div>
            }
        }
    </div>
</div>

@code {
    [Parameter]
    public string? Id { get; set; }

    private NewsItem? _newsItem;
    private List<NewsItem> _relatedNews = [];
    private bool _isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _isLoading = true;
            _newsItem = await NewsService.GetNewsItemByIdAsync(Id);

            if (_newsItem?.Company is not null)
            {
                _relatedNews = await NewsService.GetRelatedNewsAsync(_newsItem.Id);
            }
        }
        catch (Exception ex)
        {
            // Log error if needed
            Console.WriteLine($"Error loading news detail: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        // Handle navigation to different news items
        if (!_isLoading && _newsItem?.Id != Id)
        {
            await OnInitializedAsync();
        }
    }

    private void OnRelatedNewsClick(NewsItem item)
    {
        NavigationManager.NavigateTo($"/news/{item.Id}");
    }
}

<style>
    .news-detail {
        max-width: 900px;
        margin: 0 auto;
        padding: 24px 16px;
    }

    .content-container {
        background: white;
        border-radius: 8px;
        padding: 24px;
    }

    .news-header {
        margin-bottom: 24px;
    }

    .news-header h1 {
        font-size: 28px;
        margin: 16px 0 8px 0;
        color: #1a1a1a;
    }

    .news-meta {
        display: flex;
        gap: 12px;
        color: #666;
        margin-bottom: 12px;
        font-size: 14px;
    }

    .news-tags {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
    }

    .news-content {
        line-height: 1.8;
        font-size: 16px;
        color: #333;
        margin-bottom: 40px;
    }

    .company-info, .related-news {
        margin-top: 40px;
        padding-top: 24px;
        border-top: 1px solid #f0f0f0;
    }

    .company-info h2, .related-news h2 {
        font-size: 20px;
        margin-bottom: 16px;
        color: #1a1a1a;
    }

    .heat-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
    }

    .heat-value {
        font-weight: 500;
        min-width: 30px;
    }

    .heat-bar {
        flex: 1;
        height: 8px;
        background-color: #f0f0f0;
        border-radius: 4px;
        overflow: hidden;
    }

    .heat-fill {
        height: 100%;
        background: linear-gradient(90deg, #52c41a 0%, #faad14 50%, #f5222d 100%);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .active-stocks {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        max-width: 300px;
    }

    .positive {
        color: #f5222d;
    }

    .negative {
        color: #52c41a;
    }

    .loading {
        text-align: center;
        padding: 60px 40px;
        font-size: 16px;
        color: #666;
    }

    .loading p {
        margin-top: 16px;
        font-size: 16px;
    }

    .error {
        text-align: center;
        padding: 40px;
    }
</style>
