<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
        <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AntDesign" Version="1.4.3" />
        <PackageReference Include="AntDesign.Charts" Version="0.7.3" />
        <PackageReference Include="AntDesign.Icons" Version="0.1.1" />
        <PackageReference Include="Coravel" Version="6.0.2" />
        <PackageReference Include="DuckDB.NET.Data.Full" Version="1.3.2" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.7" />
        <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
        <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="4.14.0">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
        <PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.7.0-preview.1.25356.2" />
        <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
        <PackageReference Include="Ulid" Version="1.3.4" />
    </ItemGroup>

    <ItemGroup>
      <AdditionalFiles Include="Layout\MainLayout.razor" />
      <AdditionalFiles Include="Layout\NavMenu.razor" />
    </ItemGroup>

</Project>
