using TimeLens.Client.Models;
using System.Collections.Concurrent;
using TimeLens.Client.Data.Repositories;

namespace TimeLens.Client.Services
{
    public class NewsService(
        ILatestNewsSyncStateRepository latestNewsSyncStateRepository,
        IConceptThemeRepository conceptThemeRepository,
        INewsItemRepository newsItemRepository,
        INewsItemConceptThemeRepository newsItemConceptThemeRepository) {
        private readonly ConcurrentBag<NewsItem> _newsItems = [];
        private readonly Lock _lockObject = new();

        // 新浪财经全量数据：https://zhibo.sina.com.cn/api/zhibo/feed?page=1&page_size=20&zhibo_id=152&tag_id=0&dire=f&dpc=1&pagesize=20&_=1751557422167
        // 新浪财经增量数据：https://zhibo.sina.com.cn/api/zhibo/feed?page=1&page_size=20&zhibo_id=152&tag_id=0&dire=f&dpc=1&pagesize=20&id=「最新的 id」&type=0/1&_=1751557422167
        // 东方财富：https://np-weblist.eastmoney.com/comm/web/getFastNewsList?client=web&biz=web_724&fastColumn=102&sortEnd=&pageSize=50&req_trace=1751557872993&_=1751557872994
        // 同花顺：https://news.10jqka.com.cn/tapp/news/push/stock/?page=1&tag=&track=website&pagesize=400
        
        /// <summary>
        /// 更新新闻数据（由后台任务调用）
        /// </summary>
        public async Task AppendNewsItemsAsync(NewsItem newItem, string maxSyncId, SiteCategory siteCategory, List<ConceptTheme> conceptThemes)
        {
            // 查看今天有没有相关的概念主题，插入概念主题
            var existingConceptThemes = await conceptThemeRepository.GetThemesCreatedAfterAsync(DateTime.Today);
            var conceptThemesToInsert = conceptThemes
                .Where(ct => existingConceptThemes.All(exist => exist.Name != ct.Name))
                .ToList();
            var insertedConceptThemes = await conceptThemeRepository.AddRangeAsync(conceptThemesToInsert);
            var conceptThemeIds = insertedConceptThemes.Select(ct => ct.Id).ToArray();
            // 查看今天有没有相关的新闻
            var existNewsItems = await newsItemRepository.SearchByTitleAsync(newItem.Title);
            var insertedNew = existNewsItems.FirstOrDefault() ?? await newsItemRepository.AddAsync(newItem);
            // 插入关联表
            if(conceptThemeIds.Length > 0)
                await newsItemConceptThemeRepository.AddNewsItemConceptThemesAsync(insertedNew.Id, conceptThemeIds);
            // 更新最新记录
            await latestNewsSyncStateRepository.UpsertAsync(siteCategory, maxSyncId);
        }

        public async Task<List<NewsItem>> GetNewsItemsAsync(string? searchTerm, string? company, NewsCategory? category = null)
        {
            // 从数据库中查询
            List<NewsItem> query = [];
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = (await newsItemRepository.GetPagedAsync(n => n.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase), n => n.PublishDate)).ToList();
            }
            if (!string.IsNullOrEmpty(company))
            {
                query = query.Where(n => n.Company == company).ToList();
            }

            if (category.HasValue)
            {
                query = query.Where(n => n.Category == category.Value).ToList();
            }

            return query;
        }

        public async Task<NewsItem?> GetNewsItemByIdAsync(string? id)
        {
            return await newsItemRepository.GetByIdAsync(id);
        }

        public Task<List<NewsItem>> GetRelatedNewsAsync(string companyName, string? excludeNewsId = null)
        {
            return Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var query = _newsItems.Where(n => n.Company == companyName);

                    if (!string.IsNullOrEmpty(excludeNewsId))
                    {
                        query = query.Where(n => n.Id != excludeNewsId);
                    }

                    return query.OrderByDescending(n => n.PublishDate).Take(5).ToList();
                }
            });
        }
    }
}
